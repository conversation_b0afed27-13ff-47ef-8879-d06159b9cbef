import Image from 'next/image';
import { memo } from 'react';
import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';
import { SectionHeader } from '../primitives';
import {
	dallE3Examples,
	gptImage1Examples,
	imagen3Examples,
	GeneratedImageDetails,
} from '@/models/image/image-generation-examples';
import {
	lumaDreamMachineExamples,
	klingV16StandardExamples,
	miniMaxV1Examples,
	GeneratedVideoDetails,
} from '@/models/video/video-generation-examples';

const supabaseProductDemosStorageUrl =
	'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/product-demos/landing-page';

export const SkeletonOne = memo(() => {
	return (
		<div className="relative flex h-full w-full items-center justify-center overflow-hidden rounded-lg bg-black">
			<video
				className="h-full w-full object-cover"
				autoPlay
				loop
				muted
				playsInline
				key="video-1"
			>
				<source
					src={`${supabaseProductDemosStorageUrl}/model-selection-experience.webm`}
					type="video/webm"
				/>
				Your browser does not support the video tag.
			</video>
		</div>
	);
});
SkeletonOne.displayName = 'SkeletonOne';

export const SkeletonThree = memo(() => {
	return (
		<div className="relative flex h-full w-full items-center justify-center overflow-hidden rounded-lg bg-black">
			<video
				className="h-full w-full object-contain"
				autoPlay
				loop
				muted
				playsInline
				key="video-2"
			>
				<source
					src={`${supabaseProductDemosStorageUrl}/media-filters.webm`}
					type="video/webm"
				/>
				Your browser does not support the video tag.
			</video>
		</div>
	);
});
SkeletonThree.displayName = 'SkeletonThree';

const ghibliImage = gptImage1Examples.find((image) =>
	image.imageUrl.endsWith('gpt-image-1/56.webp')
);

const images: GeneratedImageDetails[] = [
	ghibliImage,
	...gptImage1Examples.slice(1, 2),
	...dallE3Examples.slice(0, 2),
	...imagen3Examples.slice(0, 2),
].filter((image): image is GeneratedImageDetails => image !== undefined);

const videos: GeneratedVideoDetails[] = [
	...lumaDreamMachineExamples.slice(0, 2),
	...klingV16StandardExamples.slice(0, 2),
	...miniMaxV1Examples.slice(0, 2),
];

const imageVariants = {
	whileHover: {
		scale: 1.1,
		rotate: 0,
		zIndex: 100,
	},
	whileTap: {
		scale: 1.1,
		rotate: 0,
		zIndex: 100,
	},
};

export const SkeletonTwo = memo(() => {
	return (
		<div className="relative flex h-full flex-col items-start gap-10 overflow-hidden p-8">
			<div className="-ml-20 flex flex-row">
				{images.map((image, idx) => (
					<motion.div
						variants={imageVariants}
						key={'images-first' + idx}
						style={{
							rotate: idx * 5 - 10, // Fixed rotation instead of Math.random()
						}}
						whileHover="whileHover"
						whileTap="whileTap"
						className="mt-4 -mr-4 shrink-0 overflow-hidden rounded-xl border border-neutral-100 bg-white p-1 dark:border-neutral-700 dark:bg-neutral-800"
					>
						<Image
							src={image.imageUrl}
							alt={image.prompt}
							width="500"
							height="500"
							className="h-20 w-20 shrink-0 rounded-lg object-cover md:h-40 md:w-40"
						/>
					</motion.div>
				))}
			</div>
			<div className="flex flex-row">
				{videos.map((video, idx) => (
					<motion.div
						key={'videos-second' + idx}
						style={{
							rotate: idx * -5 + 10, // Fixed rotation instead of Math.random()
						}}
						variants={imageVariants}
						whileHover="whileHover"
						whileTap="whileTap"
						className="mt-4 -mr-4 shrink-0 overflow-hidden rounded-xl border border-neutral-100 bg-white p-1 dark:border-neutral-700 dark:bg-neutral-800"
					>
						<video
							src={video.videoUrl}
							muted
							loop
							autoPlay
							playsInline
							className="h-20 w-20 shrink-0 rounded-lg object-cover md:h-40 md:w-40"
						/>
					</motion.div>
				))}
			</div>

			<div className="pointer-events-none absolute inset-y-0 left-0 z-100 h-full w-20 bg-linear-to-r from-white to-transparent dark:from-black" />
			<div className="pointer-events-none absolute inset-y-0 right-0 z-100 h-full w-20 bg-linear-to-l from-white to-transparent dark:from-black" />
		</div>
	);
});
SkeletonTwo.displayName = 'SkeletonTwo';

export const SkeletonFour = memo(() => {
	return (
		<div className="relative flex h-full w-full items-center justify-center overflow-hidden rounded-lg bg-black">
			<video
				className="h-full w-full object-cover"
				autoPlay
				loop
				muted
				playsInline
				key="video-3"
			>
				<source
					src={`${supabaseProductDemosStorageUrl}/model-switch.webm`}
					type="video/webm"
				/>
				Your browser does not support the video tag.
			</video>
		</div>
	);
});
SkeletonFour.displayName = 'SkeletonFour';

const features = [
	{
		title: 'We guide, you decide',
		description:
			'Clear strengths, side-by-side comparisons and human-friendly filters point you to the right model without any research.',
		skeleton: <SkeletonOne />,
		className:
			'col-span-1 lg:col-span-4 bg-black rounded-l-md border-b lg:border-r border-neutral-800',
	},
	{
		title: 'Stay ahead of every trend',
		description:
			"If it's buzzing — ghibli-fy, photoreal or the newest model — you'll find it live on ZECO the moment it drops.",
		skeleton: <SkeletonTwo />,
		className: 'border-b col-span-1 bg-black rounded-r-md lg:col-span-2 border-neutral-800',
	},
	{
		title: 'Create by need, not by name',
		description:
			'Filter media models by real-world goals - realism, animation, camera-control - and see what to expect before you commit.',
		skeleton: <SkeletonThree />,
		className:
			'col-span-1 lg:col-span-3 lg:border-r border-b  bg-black rounded-l-md border-neutral-800',
	},
	{
		title: 'Switch models, not your context',
		description:
			'Regenerate with another model in the same chat — no copy-paste, no re-prompting, no context switch.',
		skeleton: <SkeletonFour />,
		className:
			'col-span-1 lg:col-span-3 border-b lg:border-none bg-black rounded-r-md border-neutral-800',
	},
];

export const FeaturesDemoSection = memo(() => {
	return (
		<div className="relative z-20 mx-auto max-w-7xl py-10 lg:py-24">
			<div className="px-8">
				<SectionHeader
					header="AI is complicated."
					title="We made it simple for you."
					//description="The AI world is overwhelming. ZECO makes it effortless."
				/>
			</div>

			<div className="relative">
				<div className="mt-12 grid grid-cols-1 rounded-md lg:grid-cols-6 xl:border dark:border-neutral-800">
					{features.map((feature) => (
						<FeatureCard
							key={feature.title}
							className={feature.className}
						>
							<FeatureTitle>{feature.title}</FeatureTitle>
							<FeatureDescription>{feature.description}</FeatureDescription>
							<div className="h-full w-full">{feature.skeleton}</div>
						</FeatureCard>
					))}
				</div>
			</div>
		</div>
	);
});

FeaturesDemoSection.displayName = 'FeaturesDemoSection';

const FeatureCard = memo(
	({ children, className }: { children?: React.ReactNode; className?: string }) => {
		return (
			<div className={cn(`relative overflow-hidden p-4 sm:p-8`, className)}>{children}</div>
		);
	}
);
FeatureCard.displayName = 'FeatureCard';

const FeatureTitle = memo(({ children }: { children?: React.ReactNode }) => {
	return (
		<p className="mx-auto max-w-5xl text-left text-xl font-semibold tracking-tight text-zeco-purple md:text-2xl md:leading-snug">
			{children}
		</p>
	);
});
FeatureTitle.displayName = 'FeatureTitle';

const FeatureDescription = memo(({ children }: { children?: React.ReactNode }) => {
	return (
		<p
			className={cn(
				'mx-auto max-w-4xl text-left text-sm md:text-base',
				'text-center font-normal text-neutral-500 dark:text-neutral-300',
				'mx-0 my-2 max-w-md text-left md:text-sm'
			)}
		>
			{children}
		</p>
	);
});
FeatureDescription.displayName = 'FeatureDescription';
